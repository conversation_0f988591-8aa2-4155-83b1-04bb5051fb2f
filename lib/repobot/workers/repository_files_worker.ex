defmodule Repobot.Workers.RepositoryFilesWorker do
  @moduledoc """
  Oban worker for loading repository file trees and refreshing file content in the background.

  This worker handles the file loading logic that was previously done synchronously
  in the onboarding flow, making it non-blocking and providing progress feedback.
  """

  use Repobot.Workers.Worker, queue: :files, max_attempts: 3

  alias Repobot.{Repositories, Accounts.User, Files}

  @impl Oban.Worker
  def perform(
        %Oban.Job{
          args:
            %{
              "user_id" => user_id,
              "repository_ids" => repository_ids,
              "operation" => operation
            } = args
        } = job
      ) do
    log_job_start(job)

    # Get the topic for progress updates
    topic = Map.get(args, "topic", "repository_files:#{user_id}")
    # Get optional file paths for content refresh
    file_paths = Map.get(args, "file_paths")

    with {:ok, user} <- get_user(user_id),
         {:ok, repositories} <- get_repositories(repository_ids) do
      case operation do
        "load_trees" ->
          perform_tree_loading(repositories, user, topic, job)

        "refresh_content" ->
          perform_content_refresh(repositories, user, topic, job, file_paths)

        _ ->
          reason = "Invalid operation: #{operation}"
          send_completion_notification(topic, %{status: :error, reason: reason})
          log_job_error(job, reason)
          {:error, reason}
      end
    else
      {:error, reason} ->
        send_completion_notification(topic, %{status: :error, reason: reason})
        log_job_error(job, reason)
        {:error, reason}
    end
  rescue
    e ->
      reason = Exception.message(e)
      topic = Map.get(job.args, "topic", "repository_files:#{job.args["user_id"]}")
      send_completion_notification(topic, %{status: :error, reason: reason})
      log_job_error(job, reason, %{exception: inspect(e)})
      {:error, reason}
  end

  def perform(%Oban.Job{} = job) do
    log_job_start(job)

    reason = "Invalid job arguments - missing required parameters"
    log_job_error(job, reason, %{received_args: job.args})
    {:error, reason}
  end

  # Public API for enqueuing repository file jobs

  @doc """
  Enqueues a tree loading job for the given repositories.

  ## Parameters
  - user_id: The ID of the user
  - repository_ids: List of repository IDs to load trees for
  - topic: Optional topic for progress updates (defaults to "repository_files:<user_id>")

  ## Returns
  {:ok, %Oban.Job{}} on success, {:error, reason} on failure
  """
  def enqueue_tree_loading(user_id, repository_ids, topic \\ nil) do
    args = %{
      "user_id" => user_id,
      "repository_ids" => repository_ids,
      "operation" => "load_trees"
    }

    args = if topic, do: Map.put(args, "topic", topic), else: args

    new(args)
    |> Oban.insert()
  end

  @doc """
  Enqueues a content refresh job for the given repositories.

  ## Parameters
  - user_id: The ID of the user
  - repository_ids: List of repository IDs to refresh content for
  - topic: Optional topic for progress updates (defaults to "repository_files:<user_id>")
  - file_paths: Optional list of specific file paths to refresh. If nil, all files are refreshed.

  ## Returns
  {:ok, %Oban.Job{}} on success, {:error, reason} on failure
  """
  def enqueue_content_refresh(user_id, repository_ids, topic \\ nil, file_paths \\ nil) do
    args = %{
      "user_id" => user_id,
      "repository_ids" => repository_ids,
      "operation" => "refresh_content"
    }

    args = if topic, do: Map.put(args, "topic", topic), else: args
    args = if file_paths, do: Map.put(args, "file_paths", file_paths), else: args

    new(args)
    |> Oban.insert()
  end

  # Private functions

  defp get_user(user_id) do
    case Repo.get(User, user_id) do
      nil -> {:error, "User not found"}
      user -> {:ok, user}
    end
  end

  defp get_repositories(repository_ids) do
    repositories = Repositories.list_repositories_by_ids(repository_ids)

    if length(repositories) == length(repository_ids) do
      {:ok, repositories}
    else
      {:error, "Some repositories not found"}
    end
  end

  defp perform_tree_loading(repositories, user, topic, job) do
    send_progress_update(topic, 10, "Starting tree loading...")

    # Check which repositories need tree loading
    {_loading_states, repos_needing_refresh} = Files.RepoTree.init_loading(repositories)

    if Enum.empty?(repos_needing_refresh) do
      # All trees already loaded
      send_progress_update(topic, 90, "All trees already loaded")

      send_completion_notification(topic, %{
        "status" => "ok",
        "operation" => "load_trees",
        "repository_ids" => Enum.map(repositories, & &1.id)
      })

      log_job_success(job, %{
        repositories_count: length(repositories),
        cached: true
      })

      :ok
    else
      # Load trees for repositories that need it
      total_repos = length(repos_needing_refresh)
      _loaded_count = 0

      send_progress_update(topic, 20, "Loading #{total_repos} repository trees...")

      results =
        repos_needing_refresh
        |> Enum.with_index(1)
        |> Enum.map(fn {repo, index} ->
          case Repositories.refresh_repository_files!(repo.id, user) do
            {:ok, _} ->
              progress = 20 + floor(index * 60 / total_repos)
              send_progress_update(topic, progress, "Loaded tree for #{repo.full_name}")
              {:ok, repo.id}

            {:error, reason} ->
              send_progress_update(topic, nil, "Failed to load tree for #{repo.full_name}")
              {:error, repo.id, reason}
          end
        end)

      # Check results
      failed_repos = Enum.filter(results, &match?({:error, _, _}, &1))

      if Enum.empty?(failed_repos) do
        send_progress_update(topic, 90, "All trees loaded successfully")

        send_completion_notification(topic, %{
          "status" => "ok",
          "operation" => "load_trees",
          "repository_ids" => Enum.map(repositories, & &1.id)
        })

        log_job_success(job, %{
          repositories_count: length(repositories),
          loaded_count: total_repos
        })

        :ok
      else
        reason = "Failed to load trees for some repositories"

        send_completion_notification(topic, %{
          "status" => "error",
          "reason" => reason,
          "failed_repositories" =>
            Enum.map(failed_repos, fn {:error, repo_id, err} ->
              %{"id" => repo_id, "reason" => err}
            end)
        })

        log_job_error(job, reason, %{failed_count: length(failed_repos)})
        {:error, reason}
      end
    end
  end

  defp perform_content_refresh(repositories, user, topic, job, file_paths) do
    send_progress_update(topic, 10, "Starting content refresh...")

    # Ensure repositories have files preloaded with content
    repositories_with_files = Files.load_repositories_with_files(repositories)

    # Get files that need content refresh
    files_to_refresh =
      repositories_with_files
      |> Enum.flat_map(fn repo ->
        case repo.files do
          %Ecto.Association.NotLoaded{} ->
            []

          files when is_list(files) ->
            files
        end
      end)
      |> Enum.filter(&(&1.type == "file"))
      |> then(fn files ->
        if file_paths do
          # Filter to only include files with paths in the file_paths list
          file_paths_set = MapSet.new(file_paths)
          Enum.filter(files, &MapSet.member?(file_paths_set, &1.path))
        else
          # If no file_paths specified, only refresh files that don't have content
          Enum.filter(files, &(not Files.has_content?(&1)))
        end
      end)

    total_files = length(files_to_refresh)

    if total_files == 0 do
      # No files to refresh, complete immediately
      send_completion_notification(topic, %{
        "status" => "ok",
        "operation" => "refresh_content",
        "repository_ids" => Enum.map(repositories, & &1.id)
      })

      log_job_success(job, %{
        repositories_count: length(repositories),
        files_count: 0
      })

      :ok
    else
      file_type_msg = if file_paths, do: "common files", else: "files"
      send_progress_update(topic, 20, "Refreshing #{total_files} #{file_type_msg}...")

      github_api = Application.get_env(:repobot, :github_api, Repobot.GitHub)
      github_client = github_api.client(user)

      # Process files with progress updates
      results =
        files_to_refresh
        |> Task.async_stream(
          fn file ->
            repo = Enum.find(repositories_with_files, &(&1.id == file.repository_id))

            case github_api.get_file_content(github_client, repo.owner, repo.name, file.path) do
              {:ok, content, _response} ->
                {:ok, _} =
                  Repobot.RepositoryFiles.update_repository_file(file, %{
                    content: content,
                    content_updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
                  })

                :ok

              {:error, reason} ->
                {:error, reason}
            end
          end,
          ordered: false,
          max_concurrency: 5,
          timeout: 30_000
        )
        |> Stream.with_index(1)
        |> Enum.reduce_while([], fn {result, index}, acc ->
          # Calculate and send progress
          progress = 20 + floor(index * 60 / total_files)
          send_progress_update(topic, progress, "Refreshed #{index}/#{total_files} files")

          case result do
            {:ok, :ok} ->
              {:cont, [:ok | acc]}

            {:ok, {:error, reason}} ->
              {:halt, {:error, reason}}

            {:exit, reason} ->
              {:halt, {:error, reason}}
          end
        end)

      case results do
        {:error, reason} ->
          send_completion_notification(topic, %{"status" => "error", "reason" => reason})
          log_job_error(job, reason)
          {:error, reason}

        _success_list ->
          send_progress_update(topic, 90, "Content refresh complete, reloading repositories...")

          # Reload repositories with fresh content
          refreshed_repos = Files.load_repositories_with_files(repositories)

          send_completion_notification(topic, %{
            "status" => "ok",
            "operation" => "refresh_content",
            "repository_ids" => Enum.map(refreshed_repos, & &1.id)
          })

          log_job_success(job, %{
            repositories_count: length(repositories),
            files_count: total_files
          })

          :ok
      end
    end
  end

  defp send_progress_update(topic, progress, message) do
    # Use a safe atom conversion for the topic
    channel = String.to_existing_atom(topic)

    Oban.Notifier.notify(Oban, channel, %{
      "event" => "repository_files_progress",
      "progress" => progress,
      "message" => message
    })
  rescue
    ArgumentError ->
      # If the atom doesn't exist, create it safely (only for known patterns)
      if String.starts_with?(topic, "repository_files:") do
        channel = String.to_atom(topic)

        Oban.Notifier.notify(Oban, channel, %{
          "event" => "repository_files_progress",
          "progress" => progress,
          "message" => message
        })
      end
  end

  defp send_completion_notification(topic, result) do
    # Use a safe atom conversion for the topic
    channel = String.to_existing_atom(topic)

    notification = %{
      "event" => "repository_files_complete",
      "result" => result
    }

    Oban.Notifier.notify(Oban, channel, notification)
  rescue
    ArgumentError ->
      # If the atom doesn't exist, create it safely (only for known patterns)
      if String.starts_with?(topic, "repository_files:") do
        channel = String.to_atom(topic)

        notification = %{
          "event" => "repository_files_complete",
          "result" => result
        }

        Oban.Notifier.notify(Oban, channel, notification)
      end
  end
end
